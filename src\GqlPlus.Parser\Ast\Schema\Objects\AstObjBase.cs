﻿using GqlPlus.Abstractions.Schema;

namespace GqlPlus.Ast.Schema.Objects;

internal abstract record class AstObjBase<TObjArg>(
  ITokenAt At,
  string Name,
  string Description
) : AstObjType(At, Name, Description)
  , IGqlpObjBase<TObjArg>
  where TObjArg : IGqlpObjArg
{
  public IGqlpObjArg[] Args { get; set; } = [];

  public override string FullType => Args
    .Bracket("<", ">")
    .Prepend(base.FullType)
    .Joined();

  IEnumerable<IGqlpObjArg> IGqlpObjBase.Args => Args.Cast<IGqlpObjArg>();

  public void SetName(string name) => Name = name;

  public bool Equals(IGqlpObjBase? other)
    => other is IGqlpObjBase<TObjArg> objBase && Equals(objBase);
  public virtual bool Equals(AstObjBase<TObjArg>? other)
    => other is IGqlpObjBase<TObjArg> objBase && Equals(objBase);
  public bool Equals([NotNullWhen(true)] IGqlpObjBase<TObjArg>? other)
    => base.Equals(other)
    && Args.SequenceEqual(other.Args);
  public override int GetHashCode()
    => HashCode.Combine(base.GetHashCode(), Args.Length);
  internal override IEnumerable<string?> GetFields()
    => base.GetFields()
    .Concat(Args.Bracket("<", ">"));
}
